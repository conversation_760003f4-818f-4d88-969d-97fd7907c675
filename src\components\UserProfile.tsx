'use client'

import Image from 'next/image'
import Link from 'next/link'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/Dropdown'

// TypeScript interfaces for better type safety
interface UserData {
  name: string
  role: string
  avatar: string
  status?: 'online' | 'offline' | 'away'
}

interface UserProfileProps {
  user?: UserData
  onLogout?: () => void
}

export function UserProfile({
  user = {
    name: 'Username',
    role: 'Role',
    avatar: '/1.png',
    status: 'online',
  },
  onLogout,
}: UserProfileProps) {
  const handleLogout = () => {
    if (onLogout) {
      onLogout()
    } else {
      // Default logout behavior - redirect to logout page
      window.location.href = '/logout'
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case ',':
          event.preventDefault()
          window.location.href = '/user/settings'
          break
        case 'p':
          event.preventDefault()
          window.location.href = '/user/profile'
          break
      }
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="btn btn-ghost p-2 hover:bg-base-200 focus:bg-base-200 transition-colors"
          aria-label={`User menu for ${user.name}`}
          onKeyDown={handleKeyDown}
        >
          <div className="flex items-center gap-2">
            <div className="avatar">
              <div className="bg-base-200 mask mask-circle w-8 relative">
                <Image
                  src={user.avatar}
                  alt={`${user.name}'s avatar`}
                  width={32}
                  height={32}
                  className="object-cover"
                />
                {user.status && (
                  <div
                    className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-base-100 ${
                      user.status === 'online'
                        ? 'bg-success'
                        : user.status === 'away'
                        ? 'bg-warning'
                        : 'bg-base-300'
                    }`}
                    aria-label={`Status: ${user.status}`}
                  />
                )}
              </div>
            </div>
            <div className="text-start">
              <p className="text-sm font-medium truncate max-w-24">{user.name}</p>
              <p className="text-xs text-base-content/70 truncate max-w-24">{user.role}</p>
            </div>
            <span
              className="icon-[solar--alt-arrow-down-bold] text-base-content/50 ml-1"
              aria-hidden="true"
            />
          </div>
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-56" align="end" sideOffset={8} aria-label="User menu">
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-xs leading-none text-base-content/70">{user.role}</p>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link href="/user/profile" className="cursor-pointer">
            <span className="icon-[solar--user-rounded-bold-duotone]" aria-hidden="true" />
            <span>My Account</span>
            <DropdownMenuShortcut>⌘P</DropdownMenuShortcut>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href="/user/settings" className="cursor-pointer">
            <span className="icon-[solar--settings-bold-duotone]" aria-hidden="true" />
            <span>Settings</span>
            <DropdownMenuShortcut>⌘,</DropdownMenuShortcut>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href="/help" className="cursor-pointer">
            <span className="icon-[solar--question-circle-bold-duotone]" aria-hidden="true" />
            <span>Help & Support</span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem variant="destructive" onClick={handleLogout} className="cursor-pointer">
          <span className="icon-[solar--logout-bold-duotone]" aria-hidden="true" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
